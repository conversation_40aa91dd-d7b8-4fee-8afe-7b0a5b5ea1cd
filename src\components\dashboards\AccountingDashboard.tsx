import React, { useState } from 'react';
import { Payment } from '../../types';

export const AccountingDashboard: React.FC = () => {
  const [payments] = useState<Payment[]>([
    { id: '1', clientName: 'ABC Corp', amount: 5000, dueDate: new Date('2024-01-15'), status: 'overdue', daysDelayed: 10 },
    { id: '2', clientName: 'XYZ Ltd', amount: 3000, dueDate: new Date('2024-01-20'), status: 'pending', daysDelayed: 0 },
  ]);

  const totalUncollected = payments.filter(p => p.status !== 'collected').reduce((sum, p) => sum + p.amount, 0);
  const delayedPayments = payments.filter(p => p.daysDelayed > 7).length;

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">🧾 Accounting Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-red-50 p-6 rounded-lg border border-red-200">
          <h3 className="text-lg font-semibold text-red-800">🔴 Total Uncollected</h3>
          <p className="text-3xl font-bold text-red-600">${totalUncollected.toLocaleString()}</p>
        </div>
        
        <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
          <h3 className="text-lg font-semibold text-yellow-800">⏰ Delayed 7+ Days</h3>
          <p className="text-3xl font-bold text-yellow-600">{delayedPayments}</p>
        </div>
        
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h3 className="text-lg font-semibold text-blue-800">📆 Weekly Chart</h3>
          <div className="h-20 bg-blue-100 rounded mt-2 flex items-center justify-center">
            Chart Placeholder
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold">🔍 Payment Records</h3>
          <button className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
            📤 Export Report
          </button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-50">
                <th className="border border-gray-300 p-2 text-left">Client</th>
                <th className="border border-gray-300 p-2 text-left">Amount</th>
                <th className="border border-gray-300 p-2 text-left">Due Date</th>
                <th className="border border-gray-300 p-2 text-left">Status</th>
                <th className="border border-gray-300 p-2 text-left">Days Delayed</th>
              </tr>
            </thead>
            <tbody>
              {payments.map(payment => (
                <tr key={payment.id}>
                  <td className="border border-gray-300 p-2">{payment.clientName}</td>
                  <td className="border border-gray-300 p-2">${payment.amount}</td>
                  <td className="border border-gray-300 p-2">{payment.dueDate.toLocaleDateString()}</td>
                  <td className="border border-gray-300 p-2">
                    <span className={`px-2 py-1 rounded text-sm ${
                      payment.status === 'collected' ? 'bg-green-100 text-green-800' :
                      payment.status === 'overdue' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {payment.status}
                    </span>
                  </td>
                  <td className="border border-gray-300 p-2">{payment.daysDelayed}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};