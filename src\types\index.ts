export interface User {
  id: string;
  username: string;
  role: 'admin' | 'accounting' | 'logistics';
  name: string;
  email?: string;
  createdAt: Date;
  lastLogin?: Date;
}

export interface Payment {
  id: string;
  clientName: string;
  amount: number;
  dueDate: Date;
  status: 'pending' | 'collected' | 'overdue';
  daysDelayed: number;
  remarks?: string;
}

export interface Delivery {
  id: string;
  clientName: string;
  address: string;
  expectedDate: Date;
  status: 'pending' | 'in-transit' | 'delivered';
  deliveryReport?: string;
}

export interface ActivityLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  timestamp: Date;
  details: string;
}