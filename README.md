# Business Dashboard

A React TypeScript application for managing business operations with role-based dashboards for accounting and logistics teams.

## Features

- **Role-based Authentication**: Login with different roles (Admin, Accounting, Logistics)
- **Accounting Dashboard**: Track payments, overdue amounts, and financial metrics
- **Logistics Dashboard**: Manage deliveries, schedules, and logistics operations
- **Responsive Design**: Mobile-first design using TailwindCSS
- **TypeScript**: Full type safety and better development experience

## Tech Stack

- **Frontend**: React 18.2.0 with TypeScript
- **Backend**: Firebase (Authentication, Firestore, Storage)
- **Styling**: TailwindCSS 3.3.0
- **Build Tool**: Create React App
- **Package Manager**: npm

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up Firebase:
   - Follow the detailed guide in `FIREBASE_SETUP.md`
   - Copy `.env.example` to `.env` and fill in your Firebase config

4. Start the development server:
   ```bash
   npm start
   ```

5. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

### Available Scripts

- `npm start` - Runs the app in development mode
- `npm test` - Launches the test runner
- `npm run build` - Builds the app for production
- `npm run eject` - Ejects from Create React App (one-way operation)

## Project Structure

```
src/
├── components/
│   ├── Login.tsx                 # Login component with role selection
│   └── dashboards/
│       ├── AccountingDashboard.tsx  # Accounting team dashboard
│       └── LogisticsDashboard.tsx   # Logistics team dashboard
├── types/
│   └── index.ts                  # TypeScript type definitions
├── App.tsx                       # Main application component
├── index.tsx                     # Application entry point
└── index.css                     # TailwindCSS imports
```

## Usage

1. **Login**: Select a role (Admin, Accounting, or Logistics) and enter any username/password
2. **Accounting Dashboard**: View payment records, overdue amounts, and export reports
3. **Logistics Dashboard**: Track deliveries, schedules, and upload reports
4. **Admin Dashboard**: Basic admin interface (features coming soon)

## Recent Improvements

### Fixed Issues
- ✅ Added proper TypeScript type annotations
- ✅ Improved accessibility with proper labels and ARIA attributes
- ✅ Added focus states and keyboard navigation
- ✅ Fixed button types and form validation
- ✅ Enhanced user experience with better styling

### Code Quality
- ✅ Removed unused React imports (using new JSX transform)
- ✅ Added proper event handler types
- ✅ Improved component structure and organization
- ✅ Added placeholder functionality for buttons

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is private and proprietary.
