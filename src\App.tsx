import { useState } from 'react';
import { Login } from './components/Login';
import { AccountingDashboard } from './components/dashboards/AccountingDashboard';
import { LogisticsDashboard } from './components/dashboards/LogisticsDashboard';
import { User } from './types';

function App() {
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  const handleLogin = (loginData: { username: string; role: 'admin' | 'accounting' | 'logistics' }) => {
    setCurrentUser({
      id: '1',
      username: loginData.username,
      role: loginData.role,
      name: loginData.username,
      createdAt: new Date(),
    });
  };

  const handleLogout = () => {
    setCurrentUser(null);
  };

  if (!currentUser) {
    return <Login onLogin={handleLogin} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold">Business Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">Welcome, {currentUser.name}</span>
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                {currentUser.role}
              </span>
              <button
                type="button"
                onClick={handleLogout}
                className="text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 rounded px-2 py-1 transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      <main>
        {currentUser.role === 'accounting' && <AccountingDashboard />}
        {currentUser.role === 'logistics' && <LogisticsDashboard />}
        {currentUser.role === 'admin' && (
          <div className="p-6">
            <h1 className="text-3xl font-bold">👤 Admin Dashboard</h1>
            <p className="mt-4 text-gray-600">Admin features coming soon...</p>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;