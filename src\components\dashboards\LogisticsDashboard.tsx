import { useState } from 'react';
import { Delivery } from '../../types';

export const LogisticsDashboard: React.FC = () => {
  const [deliveries] = useState<Delivery[]>([
    { id: '1', clientName: 'ABC Corp', address: '123 Main St', expectedDate: new Date('2024-01-25'), status: 'pending' },
    { id: '2', clientName: 'XYZ Ltd', address: '456 Oak Ave', expectedDate: new Date('2024-01-22'), status: 'delivered' },
  ]);

  const pendingDeliveries = deliveries.filter((d: Delivery) => d.status === 'pending').length;
  const deliveredThisWeek = deliveries.filter((d: Delivery) => d.status === 'delivered').length;

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">🚚 Logistics Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
          <h3 className="text-lg font-semibold text-orange-800">📦 Pending Deliveries</h3>
          <p className="text-3xl font-bold text-orange-600">{pendingDeliveries}</p>
        </div>

        <div className="bg-green-50 p-6 rounded-lg border border-green-200">
          <h3 className="text-lg font-semibold text-green-800">✅ Delivered This Week</h3>
          <p className="text-3xl font-bold text-green-600">{deliveredThisWeek}</p>
        </div>

        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h3 className="text-lg font-semibold text-blue-800">📅 Schedule</h3>
          <button
            type="button"
            className="mt-2 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            onClick={() => console.log('Upload report functionality to be implemented')}
          >
            📝 Upload Report
          </button>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-xl font-semibold mb-4">Delivery Schedule</h3>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-50">
                <th className="border border-gray-300 p-2 text-left">Client</th>
                <th className="border border-gray-300 p-2 text-left">Address</th>
                <th className="border border-gray-300 p-2 text-left">Expected Date</th>
                <th className="border border-gray-300 p-2 text-left">Status</th>
              </tr>
            </thead>
            <tbody>
              {deliveries.map((delivery: Delivery) => (
                <tr key={delivery.id}>
                  <td className="border border-gray-300 p-2">{delivery.clientName}</td>
                  <td className="border border-gray-300 p-2">{delivery.address}</td>
                  <td className="border border-gray-300 p-2">{delivery.expectedDate.toLocaleDateString()}</td>
                  <td className="border border-gray-300 p-2">
                    <span className={`px-2 py-1 rounded text-sm ${
                      delivery.status === 'delivered' ? 'bg-green-100 text-green-800' :
                      delivery.status === 'in-transit' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {delivery.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};