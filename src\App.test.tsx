import { render, screen } from '@testing-library/react';
import App from './App';

test('renders login form initially', () => {
  render(<App />);
  const loginElement = screen.getByText(/login/i);
  expect(loginElement).toBeInTheDocument();
});

test('renders username input', () => {
  render(<App />);
  const usernameInput = screen.getByLabelText(/username/i);
  expect(usernameInput).toBeInTheDocument();
});

test('renders role selector', () => {
  render(<App />);
  const roleSelect = screen.getByLabelText(/role/i);
  expect(roleSelect).toBeInTheDocument();
});
